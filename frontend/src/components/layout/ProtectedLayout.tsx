'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Home,
  MessageSquare,
  Settings,
  LogOut,
  Menu,
  X,
  User,
  Sun,
  Moon
} from 'lucide-react';

interface ProtectedLayoutProps {
  children: React.ReactNode;
}

const ProtectedLayout: React.FC<ProtectedLayoutProps> = ({ children }) => {
  const router = useRouter();
  const pathname = usePathname();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [darkMode, setDarkMode] = useState(false);

  const navigation = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: Home,
      description: 'Overview & Analytics'
    },
    {
      name: 'Legal Assistant',
      href: '/legal-assistant',
      icon: MessageSquare,
      description: 'AI Chat & Documents'
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: Settings,
      description: 'System Configuration'
    }
  ];

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  const handleLogout = () => {
    router.push('/login');
  };

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${darkMode ? 'dark' : ''}`}>
      {/* Mobile sidebar backdrop */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Desktop Layout */}
      <div className="hidden lg:flex lg:h-screen lg:overflow-hidden">
        {/* Desktop Sidebar */}
        <aside className="w-80 bg-white dark:bg-gray-900 shadow-xl border-r border-gray-200 dark:border-gray-800 flex-shrink-0 overflow-y-auto">
          <div className="flex h-full flex-col">
            {/* Logo */}
            <div className="flex h-16 items-center px-6 border-b border-gray-200 dark:border-gray-800">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 rounded-lg flex items-center justify-center">
                  <span className="text-white dark:text-gray-900 font-bold text-sm">LS</span>
                </div>
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  Legal System
                </h1>
              </div>
            </div>

            {/* Navigation */}
            <nav className="flex-1 px-4 py-6">
              <div className="space-y-1">
                <div className="px-3 py-2">
                  <h2 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Navigation
                  </h2>
                </div>
                {navigation.map((item) => {
                  const isActive = pathname === item.href;
                  return (
                    <div
                      key={item.name}
                      className={`w-full group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 cursor-pointer ${
                        isActive
                          ? 'bg-gray-900 text-white shadow-lg dark:bg-white dark:text-gray-900'
                          : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white'
                      }`}
                      onClick={() => router.push(item.href)}
                    >
                      <div className={`mr-3 p-1 rounded-lg transition-colors ${
                        isActive 
                          ? 'bg-white/20 dark:bg-gray-900/20' 
                          : 'group-hover:bg-gray-200 dark:group-hover:bg-gray-700'
                      }`}>
                        <item.icon className="h-5 w-5" />
                      </div>
                      <div className="flex-1 text-left">
                        <div className="font-medium">{item.name}</div>
                        <div className={`text-xs ${
                          isActive 
                            ? 'text-white/70 dark:text-gray-900/70' 
                            : 'text-gray-500 dark:text-gray-400'
                        }`}>
                          {item.description}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </nav>

            {/* User section */}
            <div className="border-t border-gray-200 dark:border-gray-800 p-4">
              <div className="space-y-4">
                {/* User Profile */}
                <div className="flex items-center space-x-3 p-3 rounded-xl bg-gray-50 dark:bg-gray-800/50">
                  <div className="h-10 w-10 rounded-full bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 flex items-center justify-center">
                    <User className="h-5 w-5 text-white dark:text-gray-900" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      Demo User
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      <EMAIL>
                    </p>
                  </div>
                  <button
                    onClick={toggleDarkMode}
                    className="p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-200 dark:hover:text-gray-300 dark:hover:bg-gray-700 transition-colors"
                  >
                    {darkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
                  </button>
                </div>

                {/* Quick Stats */}
                <div className="grid grid-cols-2 gap-3">
                  <div className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50">
                    <div className="text-lg font-semibold text-gray-900 dark:text-white">24</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Documents</div>
                  </div>
                  <div className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50">
                    <div className="text-lg font-semibold text-gray-900 dark:text-white">156</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Chats</div>
                  </div>
                </div>

                {/* Logout Button */}
                <button
                  onClick={handleLogout}
                  className="w-full flex items-center justify-center px-4 py-3 text-sm font-medium text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-xl transition-colors border border-red-200 dark:border-red-800"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign Out
                </button>
              </div>
            </div>
          </div>
        </aside>

        {/* Desktop Main content */}
        <div className="flex-1 flex flex-col min-w-0 overflow-hidden">
          {/* Top bar */}
          <header className="sticky top-0 z-40 flex h-16 items-center border-b border-gray-200 dark:border-gray-800 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md px-4 shadow-sm sm:px-6 lg:px-8">
            <div className="flex flex-1 gap-x-4 self-stretch items-center">
              <div className="flex-1">
                <h1 className="text-lg font-semibold text-gray-900 dark:text-white capitalize">
                  {pathname.split('/').pop() || 'Dashboard'}
                </h1>
              </div>
              <div className="flex items-center gap-2">
                <button className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
                  <span className="sr-only">Notifications</span>
                  <div className="relative">
                    <div className="h-5 w-5 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                    <div className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></div>
                  </div>
                </button>
              </div>
            </div>
          </header>

          {/* Page content */}
          <main className="flex-1 overflow-y-auto py-6 px-4 sm:px-6 lg:px-8 relative">
            <div className="absolute inset-0 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-900 pointer-events-none" />
            <div className="relative z-10">
              {children}
            </div>
          </main>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="lg:hidden">
        {/* Mobile Sidebar */}
        <motion.aside
          initial={false}
          animate={{
            x: sidebarOpen ? 0 : -320,
          }}
          transition={{ type: 'spring', damping: 30, stiffness: 300 }}
          className="fixed inset-y-0 left-0 z-50 w-80 bg-white dark:bg-gray-900 shadow-xl border-r border-gray-200 dark:border-gray-800 overflow-y-auto"
        >
          <div className="flex h-full flex-col">
            {/* Logo */}
            <div className="flex h-16 items-center justify-between px-6 border-b border-gray-200 dark:border-gray-800">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 rounded-lg flex items-center justify-center">
                  <span className="text-white dark:text-gray-900 font-bold text-sm">LS</span>
                </div>
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  Legal System
                </h1>
              </div>
              <button
                onClick={() => setSidebarOpen(false)}
                className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Navigation */}
            <nav className="flex-1 px-4 py-6">
              <div className="space-y-1">
                <div className="px-3 py-2">
                  <h2 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Navigation
                  </h2>
                </div>
                {navigation.map((item) => {
                  const isActive = pathname === item.href;
                  return (
                    <div
                      key={item.name}
                      className={`w-full group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 cursor-pointer ${
                        isActive
                          ? 'bg-gray-900 text-white shadow-lg dark:bg-white dark:text-gray-900'
                          : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white'
                      }`}
                      onClick={() => {
                        router.push(item.href);
                        setSidebarOpen(false);
                      }}
                    >
                      <div className={`mr-3 p-1 rounded-lg transition-colors ${
                        isActive
                          ? 'bg-white/20 dark:bg-gray-900/20'
                          : 'group-hover:bg-gray-200 dark:group-hover:bg-gray-700'
                      }`}>
                        <item.icon className="h-5 w-5" />
                      </div>
                      <div className="flex-1 text-left">
                        <div className="font-medium">{item.name}</div>
                        <div className={`text-xs ${
                          isActive
                            ? 'text-white/70 dark:text-gray-900/70'
                            : 'text-gray-500 dark:text-gray-400'
                        }`}>
                          {item.description}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </nav>

            {/* User section */}
            <div className="border-t border-gray-200 dark:border-gray-800 p-4">
              <div className="space-y-4">
                {/* User Profile */}
                <div className="flex items-center space-x-3 p-3 rounded-xl bg-gray-50 dark:bg-gray-800/50">
                  <div className="h-10 w-10 rounded-full bg-gradient-to-br from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 flex items-center justify-center">
                    <User className="h-5 w-5 text-white dark:text-gray-900" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      Demo User
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      <EMAIL>
                    </p>
                  </div>
                  <button
                    onClick={toggleDarkMode}
                    className="p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-200 dark:hover:text-gray-300 dark:hover:bg-gray-700 transition-colors"
                  >
                    {darkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
                  </button>
                </div>

                {/* Quick Stats */}
                <div className="grid grid-cols-2 gap-3">
                  <div className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50">
                    <div className="text-lg font-semibold text-gray-900 dark:text-white">24</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Documents</div>
                  </div>
                  <div className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50">
                    <div className="text-lg font-semibold text-gray-900 dark:text-white">156</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Chats</div>
                  </div>
                </div>

                {/* Logout Button */}
                <button
                  onClick={handleLogout}
                  className="w-full flex items-center justify-center px-4 py-3 text-sm font-medium text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-xl transition-colors border border-red-200 dark:border-red-800"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign Out
                </button>
              </div>
            </div>
          </div>
        </motion.aside>

        {/* Mobile Main content */}
        <div className="min-h-screen">
          {/* Mobile Top bar */}
          <header className="sticky top-0 z-40 flex h-16 items-center border-b border-gray-200 dark:border-gray-800 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md px-4 shadow-sm">
            <button
              type="button"
              className="p-2.5 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
              onClick={() => setSidebarOpen(true)}
            >
              <Menu className="h-6 w-6" />
            </button>

            <div className="flex flex-1 gap-x-4 self-stretch items-center ml-4">
              <div className="flex-1">
                <h1 className="text-lg font-semibold text-gray-900 dark:text-white capitalize">
                  {pathname.split('/').pop() || 'Dashboard'}
                </h1>
              </div>

              <div className="flex items-center gap-2">
                <button className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
                  <span className="sr-only">Notifications</span>
                  <div className="relative">
                    <div className="h-5 w-5 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                    <div className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></div>
                  </div>
                </button>
              </div>
            </div>
          </header>

          {/* Mobile Page content */}
          <main className="py-6 px-4 relative">
            <div className="absolute inset-0 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-900 pointer-events-none" />
            <div className="relative z-10">
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};

export default ProtectedLayout;
